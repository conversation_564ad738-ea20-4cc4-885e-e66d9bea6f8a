/**
 * 第9批次地形与环境系统节点统一导出
 * 节点241-270：完整的30个节点实现
 */

// 地形与环境系统节点 (241-250)
export {
  EnableRefractionNode,
  CreateVegetationNode,
  AddGrassNode,
  AddTreesNode,
  CreateWeatherSystemNode,
  EnableRainNode,
  EnableSnowNode,
  SetWindDirectionNode,
  SetWindStrengthNode,
  SetTimeOfDayNode
} from './TerrainEnvironmentNodesBatch9';

// 编辑器项目管理节点 (251-270)
export {
  CreateProjectNode,
  OpenProjectNode,
  SaveProjectNode,
  CloseProjectNode,
  ExportProjectNode,
  ImportProjectNode,
  SetProjectSettingsNode,
  GetProjectInfoNode,
  ImportAssetNode,
  DeleteAssetNode,
  RenameAssetNode,
  MoveAssetNode,
  CreateFolderNode,
  GetAssetInfoNode,
  GenerateThumbnailNode,
  CreateEntityNode,
  DeleteEntityNode,
  SelectEntityNode,
  DuplicateEntityNode,
  GroupEntitiesNode
} from './EditorProjectNodesBatch9';

/**
 * 第9批次节点映射表
 * 用于节点注册和类型检查
 */
export const BATCH9_NODE_MAPPING = {
  // 地形与环境系统节点 (241-250)
  'water/refraction/enableRefraction': 'EnableRefractionNode',
  'vegetation/system/createVegetation': 'CreateVegetationNode',
  'vegetation/grass/addGrass': 'AddGrassNode',
  'vegetation/trees/addTrees': 'AddTreesNode',
  'weather/system/createWeatherSystem': 'CreateWeatherSystemNode',
  'weather/rain/enableRain': 'EnableRainNode',
  'weather/snow/enableSnow': 'EnableSnowNode',
  'weather/wind/setWindDirection': 'SetWindDirectionNode',
  'weather/wind/setWindStrength': 'SetWindStrengthNode',
  'environment/time/setTimeOfDay': 'SetTimeOfDayNode',

  // 编辑器项目管理节点 (251-270)
  'editor/project/createProject': 'CreateProjectNode',
  'editor/project/openProject': 'OpenProjectNode',
  'editor/project/saveProject': 'SaveProjectNode',
  'editor/project/closeProject': 'CloseProjectNode',
  'editor/project/exportProject': 'ExportProjectNode',
  'editor/project/importProject': 'ImportProjectNode',
  'editor/project/setProjectSettings': 'SetProjectSettingsNode',
  'editor/project/getProjectInfo': 'GetProjectInfoNode',
  'editor/asset/importAsset': 'ImportAssetNode',
  'editor/asset/deleteAsset': 'DeleteAssetNode',
  'editor/asset/renameAsset': 'RenameAssetNode',
  'editor/asset/moveAsset': 'MoveAssetNode',
  'editor/asset/createFolder': 'CreateFolderNode',
  'editor/asset/getAssetInfo': 'GetAssetInfoNode',
  'editor/asset/generateThumbnail': 'GenerateThumbnailNode',
  'editor/scene/createEntity': 'CreateEntityNode',
  'editor/scene/deleteEntity': 'DeleteEntityNode',
  'editor/scene/selectEntity': 'SelectEntityNode',
  'editor/scene/duplicateEntity': 'DuplicateEntityNode',
  'editor/scene/groupEntities': 'GroupEntitiesNode'
};

/**
 * 第9批次节点类别分组
 */
export const BATCH9_NODE_CATEGORIES = {
  WATER_EFFECTS: [
    'water/refraction/enableRefraction'
  ],
  VEGETATION_SYSTEM: [
    'vegetation/system/createVegetation',
    'vegetation/grass/addGrass',
    'vegetation/trees/addTrees'
  ],
  WEATHER_SYSTEM: [
    'weather/system/createWeatherSystem',
    'weather/rain/enableRain',
    'weather/snow/enableSnow',
    'weather/wind/setWindDirection',
    'weather/wind/setWindStrength'
  ],
  ENVIRONMENT_CONTROL: [
    'environment/time/setTimeOfDay'
  ],
  PROJECT_MANAGEMENT: [
    'editor/project/createProject',
    'editor/project/openProject',
    'editor/project/saveProject',
    'editor/project/closeProject',
    'editor/project/exportProject',
    'editor/project/importProject',
    'editor/project/setProjectSettings',
    'editor/project/getProjectInfo'
  ],
  ASSET_MANAGEMENT: [
    'editor/asset/importAsset',
    'editor/asset/deleteAsset',
    'editor/asset/renameAsset',
    'editor/asset/moveAsset',
    'editor/asset/createFolder',
    'editor/asset/getAssetInfo',
    'editor/asset/generateThumbnail'
  ],
  SCENE_EDITING: [
    'editor/scene/createEntity',
    'editor/scene/deleteEntity',
    'editor/scene/selectEntity',
    'editor/scene/duplicateEntity',
    'editor/scene/groupEntities'
  ]
};

/**
 * 获取第9批次所有节点类型
 */
export function getBatch9NodeTypes(): string[] {
  return Object.keys(BATCH9_NODE_MAPPING);
}

/**
 * 获取第9批次节点总数
 */
export function getBatch9NodeCount(): number {
  return Object.keys(BATCH9_NODE_MAPPING).length;
}

/**
 * 验证第9批次节点完整性
 */
export function validateBatch9Nodes(): boolean {
  const expectedCount = 30;
  const actualCount = getBatch9NodeCount();
  
  if (actualCount !== expectedCount) {
    console.error(`第9批次节点数量不匹配: 期望 ${expectedCount}, 实际 ${actualCount}`);
    return false;
  }
  
  console.log(`第9批次节点验证通过: ${actualCount} 个节点`);
  return true;
}

/**
 * 第9批次节点描述信息
 */
export const BATCH9_NODE_DESCRIPTIONS = {
  'water/refraction/enableRefraction': '启用水面折射效果',
  'vegetation/system/createVegetation': '创建植被系统',
  'vegetation/grass/addGrass': '添加草地植被',
  'vegetation/trees/addTrees': '添加树木植被',
  'weather/system/createWeatherSystem': '创建动态天气系统',
  'weather/rain/enableRain': '启用雨天效果',
  'weather/snow/enableSnow': '启用雪天效果',
  'weather/wind/setWindDirection': '设置环境风向',
  'weather/wind/setWindStrength': '设置环境风力强度',
  'environment/time/setTimeOfDay': '设置环境时间',
  'editor/project/createProject': '创建新的编辑器项目',
  'editor/project/openProject': '打开现有项目',
  'editor/project/saveProject': '保存当前项目',
  'editor/project/closeProject': '关闭当前项目',
  'editor/project/exportProject': '导出项目文件',
  'editor/project/importProject': '导入项目文件',
  'editor/project/setProjectSettings': '配置项目参数',
  'editor/project/getProjectInfo': '获取项目基本信息',
  'editor/asset/importAsset': '导入外部资产文件',
  'editor/asset/deleteAsset': '删除项目资产',
  'editor/asset/renameAsset': '重命名资产文件',
  'editor/asset/moveAsset': '移动资产到文件夹',
  'editor/asset/createFolder': '创建资产文件夹',
  'editor/asset/getAssetInfo': '获取资产详细信息',
  'editor/asset/generateThumbnail': '生成资产预览图',
  'editor/scene/createEntity': '在场景中创建新实体',
  'editor/scene/deleteEntity': '从场景删除实体',
  'editor/scene/selectEntity': '选择场景中的实体',
  'editor/scene/duplicateEntity': '复制选中的实体',
  'editor/scene/groupEntities': '将多个实体组合'
};
