/**
 * UI控制和工具辅助节点
 * 第10批次：UI控制和工具辅助节点（节点291-300）
 * 包含UI可见性、启用状态、层级、对齐、分布、编辑工具等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// UI控制节点（291-295）
// ============================================================================

/**
 * 设置UI可见性节点 (291)
 * 设置UI元素显示状态
 */
export class SetUIVisibleNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'visible',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.INPUT,
      description: '是否可见',
      defaultValue: true
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const visible = inputs.visible !== false;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置可见性
      element.style.display = visible ? 'block' : 'none';

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI可见性失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI启用状态节点 (292)
 * 设置UI元素交互状态
 */
export class SetUIEnabledNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'enabled',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.INPUT,
      description: '是否启用',
      defaultValue: true
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const enabled = inputs.enabled !== false;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 设置启用状态
      element.disabled = !enabled;
      
      if (element.style) {
        element.style.pointerEvents = enabled ? 'auto' : 'none';
        element.style.opacity = enabled ? '1' : '0.5';
      }

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI启用状态失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI层级节点 (293)
 * 设置UI元素渲染层级
 */
export class SetUILayerNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'zIndex',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: 'Z层级',
      defaultValue: 0
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const zIndex = inputs.zIndex || 0;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置层级
      element.style.zIndex = zIndex.toString();

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI层级失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 对齐UI元素节点 (294)
 * 对齐多个UI元素
 */
export class AlignUIElementsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'elements',
      type: SocketType.ARRAY,
      direction: SocketDirection.INPUT,
      description: 'UI元素数组'
    });
    this.addSocket({
      name: 'alignment',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '对齐方式',
      defaultValue: 'left'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'elements',
      type: SocketType.ARRAY,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素数组'
    });
  }

  public execute(inputs: any): any {
    try {
      const elements = inputs.elements;
      const alignment = inputs.alignment || 'left';

      if (!elements || !Array.isArray(elements) || elements.length === 0) {
        throw new Error('UI元素数组不能为空');
      }

      // 计算对齐基准
      let referenceValue = 0;
      
      if (alignment === 'left' || alignment === 'right') {
        // 水平对齐
        const positions = elements.map(el => parseInt(el.style.left) || 0);
        referenceValue = alignment === 'left' ? Math.min(...positions) : Math.max(...positions);
        
        elements.forEach(element => {
          if (element.style) {
            element.style.left = `${referenceValue}px`;
          }
        });
      } else if (alignment === 'top' || alignment === 'bottom') {
        // 垂直对齐
        const positions = elements.map(el => parseInt(el.style.top) || 0);
        referenceValue = alignment === 'top' ? Math.min(...positions) : Math.max(...positions);
        
        elements.forEach(element => {
          if (element.style) {
            element.style.top = `${referenceValue}px`;
          }
        });
      }

      return {
        completed: true,
        elements: elements
      };
    } catch (error) {
      console.error('对齐UI元素失败:', error);
      return {
        completed: false,
        elements: inputs.elements
      };
    }
  }
}

/**
 * 分布UI元素节点 (295)
 * 均匀分布UI元素
 */
export class DistributeUIElementsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'elements',
      type: SocketType.ARRAY,
      direction: SocketDirection.INPUT,
      description: 'UI元素数组'
    });
    this.addSocket({
      name: 'direction',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '分布方向',
      defaultValue: 'horizontal'
    });
    this.addSocket({
      name: 'spacing',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '间距',
      defaultValue: 10
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'elements',
      type: SocketType.ARRAY,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素数组'
    });
  }

  public execute(inputs: any): any {
    try {
      const elements = inputs.elements;
      const direction = inputs.direction || 'horizontal';
      const spacing = inputs.spacing || 10;

      if (!elements || !Array.isArray(elements) || elements.length === 0) {
        throw new Error('UI元素数组不能为空');
      }

      // 均匀分布元素
      elements.forEach((element, index) => {
        if (element.style) {
          if (direction === 'horizontal') {
            element.style.left = `${index * spacing}px`;
          } else if (direction === 'vertical') {
            element.style.top = `${index * spacing}px`;
          }
        }
      });

      return {
        completed: true,
        elements: elements
      };
    } catch (error) {
      console.error('分布UI元素失败:', error);
      return {
        completed: false,
        elements: inputs.elements
      };
    }
  }
}

// ============================================================================
// 工具和辅助节点（296-300）
// ============================================================================

/**
 * 启用操作手柄节点 (296)
 * 启用3D操作手柄
 */
export class EnableGizmoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'camera',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '相机对象'
    });
    this.addSocket({
      name: 'renderer',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '渲染器对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'gizmo',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '操作手柄'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const camera = inputs.camera;
      const renderer = inputs.renderer;

      if (!scene || !camera || !renderer) {
        throw new Error('场景、相机和渲染器对象不能为空');
      }

      // 创建操作手柄（模拟实现）
      const gizmo = {
        scene: scene,
        camera: camera,
        renderer: renderer,
        enabled: true,
        mode: 'translate',
        target: null,
        update: function() {
          // 更新手柄显示
        },
        setTarget: function(object: any) {
          this.target = object;
        }
      };

      return {
        completed: true,
        gizmo: gizmo
      };
    } catch (error) {
      console.error('启用操作手柄失败:', error);
      return {
        completed: false,
        gizmo: null
      };
    }
  }
}

/**
 * 设置手柄模式节点 (297)
 * 设置操作手柄模式
 */
export class SetGizmoModeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'gizmo',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '操作手柄'
    });
    this.addSocket({
      name: 'mode',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '手柄模式',
      defaultValue: 'translate'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'gizmo',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '操作手柄'
    });
  }

  public execute(inputs: any): any {
    try {
      const gizmo = inputs.gizmo;
      const mode = inputs.mode || 'translate';

      if (!gizmo) {
        throw new Error('操作手柄不能为空');
      }

      // 设置手柄模式
      gizmo.mode = mode;

      return {
        completed: true,
        gizmo: gizmo
      };
    } catch (error) {
      console.error('设置手柄模式失败:', error);
      return {
        completed: false,
        gizmo: inputs.gizmo
      };
    }
  }
}

/**
 * 启用网格节点 (298)
 * 启用场景网格显示
 */
export class EnableGridNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'size',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '网格大小',
      defaultValue: 100
    });
    this.addSocket({
      name: 'divisions',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '网格分割数',
      defaultValue: 10
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'grid',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '网格对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const size = inputs.size || 100;
      const divisions = inputs.divisions || 10;

      if (!scene) {
        throw new Error('场景对象不能为空');
      }

      // 创建网格
      const grid = new THREE.GridHelper(size, divisions);
      scene.add(grid);

      return {
        completed: true,
        grid: grid
      };
    } catch (error) {
      console.error('启用网格失败:', error);
      return {
        completed: false,
        grid: null
      };
    }
  }
}

/**
 * 设置网格大小节点 (299)
 * 设置网格间距
 */
export class SetGridSizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'grid',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '网格对象'
    });
    this.addSocket({
      name: 'size',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '网格大小',
      defaultValue: 100
    });
    this.addSocket({
      name: 'divisions',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '网格分割数',
      defaultValue: 10
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'grid',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '网格对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const grid = inputs.grid;
      const size = inputs.size || 100;
      const divisions = inputs.divisions || 10;

      if (!grid) {
        throw new Error('网格对象不能为空');
      }

      // 更新网格参数（模拟实现）
      grid.userData = grid.userData || {};
      grid.userData.size = size;
      grid.userData.divisions = divisions;

      return {
        completed: true,
        grid: grid
      };
    } catch (error) {
      console.error('设置网格大小失败:', error);
      return {
        completed: false,
        grid: inputs.grid
      };
    }
  }
}

/**
 * 启用吸附节点 (300)
 * 启用对象吸附功能
 */
export class EnableSnapNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'snapDistance',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '吸附距离',
      defaultValue: 1
    });
    this.addSocket({
      name: 'snapToGrid',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.INPUT,
      description: '吸附到网格',
      defaultValue: true
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'snapSettings',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '吸附设置'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const snapDistance = inputs.snapDistance || 1;
      const snapToGrid = inputs.snapToGrid !== false;

      if (!scene) {
        throw new Error('场景对象不能为空');
      }

      // 创建吸附设置
      const snapSettings = {
        enabled: true,
        distance: snapDistance,
        toGrid: snapToGrid,
        toObjects: true,
        snap: function(position: THREE.Vector3): THREE.Vector3 {
          // 吸附逻辑实现
          if (this.toGrid) {
            position.x = Math.round(position.x / this.distance) * this.distance;
            position.y = Math.round(position.y / this.distance) * this.distance;
            position.z = Math.round(position.z / this.distance) * this.distance;
          }
          return position;
        }
      };

      // 将吸附设置添加到场景
      scene.userData = scene.userData || {};
      scene.userData.snapSettings = snapSettings;

      return {
        completed: true,
        snapSettings: snapSettings
      };
    } catch (error) {
      console.error('启用吸附功能失败:', error);
      return {
        completed: false,
        snapSettings: null
      };
    }
  }
}
