/**
 * 编辑器项目管理节点
 * 第9批次：编辑器项目管理（节点251-270）
 * 包含项目创建、打开、保存、资产管理、场景编辑等功能
 */

import { Node, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 编辑器项目管理节点（251-258）
// ============================================================================

/**
 * 创建项目节点 (251)
 * 创建新的项目
 */
export class CreateProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'projectName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目名称',
      defaultValue: '新项目'
    });
    this.addInput({
      name: 'template',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目模板',
      defaultValue: 'empty'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectNameInput = this.getInput('projectName');
      const templateInput = this.getInput('template');

      const projectName = projectNameInput?.value || '新项目';
      const template = templateInput?.value || 'empty';

      // 创建项目对象
      const project = {
        id: Date.now().toString(),
        name: projectName,
        template: template,
        createdAt: new Date(),
        scenes: [],
        assets: [],
        settings: {
          version: '1.0.0',
          renderer: 'webgl',
          physics: 'cannon'
        }
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('创建项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

/**
 * 打开项目节点 (252)
 * 打开现有项目
 */
export class OpenProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'projectPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '项目路径'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectPathInput = this.getInput('projectPath');
      const projectPath = projectPathInput?.value;

      if (!projectPath) {
        throw new Error('项目路径不能为空');
      }

      // 模拟打开项目
      const project = {
        id: Date.now().toString(),
        name: '已打开的项目',
        path: projectPath,
        openedAt: new Date(),
        scenes: [],
        assets: []
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('打开项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

/**
 * 保存项目节点 (253)
 * 保存项目到文件
 */
export class SaveProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'savePath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '保存路径'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '保存成功'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const savePathInput = this.getInput('savePath');

      const project = projectInput?.value;
      const savePath = savePathInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 更新项目保存信息
      project.savedAt = new Date();
      if (savePath) {
        project.path = savePath;
      }

      // 模拟保存操作
      console.log('保存项目到:', savePath || '默认路径');

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const successOutput = this.getOutput('success');

      if (completedOutput) completedOutput.value = true;
      if (successOutput) successOutput.value = true;

      return true;
    } catch (error) {
      console.error('保存项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const successOutput = this.getOutput('success');

      if (completedOutput) completedOutput.value = false;
      if (successOutput) successOutput.value = false;

      return false;
    }
  }
}

/**
 * 关闭项目节点 (254)
 * 关闭当前项目
 */
export class CloseProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'saveBeforeClose',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'boolean',
      description: '关闭前保存',
      defaultValue: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'closed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '关闭成功'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const saveBeforeCloseInput = this.getInput('saveBeforeClose');

      const project = projectInput?.value;
      const saveBeforeClose = saveBeforeCloseInput?.value !== false;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 如果需要，在关闭前保存
      if (saveBeforeClose) {
        project.savedAt = new Date();
        console.log('关闭前保存项目');
      }

      // 模拟关闭操作
      project.closedAt = new Date();
      console.log('项目已关闭');

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const closedOutput = this.getOutput('closed');

      if (completedOutput) completedOutput.value = true;
      if (closedOutput) closedOutput.value = true;

      return true;
    } catch (error) {
      console.error('关闭项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const closedOutput = this.getOutput('closed');

      if (completedOutput) completedOutput.value = false;
      if (closedOutput) closedOutput.value = false;

      return false;
    }
  }
}

/**
 * 创建项目模板节点 (255)
 * 创建项目模板
 */
export class CreateProjectTemplateNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'templateName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '模板名称'
    });
    this.addInput({
      name: 'templateData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '模板数据'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'template',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '模板对象'
    });
  }

  public execute(): any {
    try {
      const templateNameInput = this.getInput('templateName');
      const templateDataInput = this.getInput('templateData');

      const templateName = templateNameInput?.value;
      const templateData = templateDataInput?.value;

      if (!templateName) {
        throw new Error('模板名称不能为空');
      }

      // 创建模板对象
      const template = {
        id: Date.now().toString(),
        name: templateName,
        data: templateData || {},
        createdAt: new Date(),
        type: 'project-template'
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const templateOutput = this.getOutput('template');

      if (completedOutput) completedOutput.value = true;
      if (templateOutput) templateOutput.value = template;

      return template;
    } catch (error) {
      console.error('创建项目模板失败:', error);

      const completedOutput = this.getOutput('completed');
      const templateOutput = this.getOutput('template');

      if (completedOutput) completedOutput.value = false;
      if (templateOutput) templateOutput.value = null;

      return null;
    }
  }
}

/**
 * 导入项目节点 (256)
 * 导入项目文件
 */
export class ImportProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'projectData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目数据'
    });
    this.addInput({
      name: 'importPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '导入路径'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectDataInput = this.getInput('projectData');
      const importPathInput = this.getInput('importPath');

      const projectData = projectDataInput?.value;
      const importPath = importPathInput?.value;

      if (!projectData) {
        throw new Error('项目数据不能为空');
      }

      // 创建导入的项目对象
      const project = {
        ...projectData,
        importedAt: new Date(),
        importPath: importPath,
        id: Date.now().toString()
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('导入项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

/**
 * 设置项目配置节点 (257)
 * 配置项目参数
 */
export class SetProjectSettingsNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'settings',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '配置设置'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目对象'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const settingsInput = this.getInput('settings');

      const project = projectInput?.value;
      const settings = settingsInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 更新项目设置
      if (settings) {
        project.settings = {
          ...project.settings,
          ...settings,
          updatedAt: new Date()
        };
      }

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = project;

      return project;
    } catch (error) {
      console.error('设置项目配置失败:', error);

      const projectInput = this.getInput('project');
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = projectInput?.value;

      return null;
    }
  }
}

/**
 * 获取项目信息节点 (258)
 * 获取项目基本信息
 */
export class GetProjectInfoNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'projectInfo',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '项目信息'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const project = projectInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 提取项目信息
      const projectInfo = {
        id: project.id,
        name: project.name,
        template: project.template,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        savedAt: project.savedAt,
        path: project.path,
        settings: project.settings,
        sceneCount: project.scenes ? project.scenes.length : 0,
        assetCount: project.assets ? project.assets.length : 0
      };

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectInfoOutput = this.getOutput('projectInfo');

      if (completedOutput) completedOutput.value = true;
      if (projectInfoOutput) projectInfoOutput.value = projectInfo;

      return projectInfo;
    } catch (error) {
      console.error('获取项目信息失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectInfoOutput = this.getOutput('projectInfo');

      if (completedOutput) completedOutput.value = false;
      if (projectInfoOutput) projectInfoOutput.value = null;

      return null;
    }
  }
}

// ============================================================================
// 资产管理节点（259-270）
// ============================================================================

/**
 * 添加资产节点 (259)
 * 向项目添加资产
 */
export class AddAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'assetData',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '资产数据'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'asset',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '资产对象'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const assetDataInput = this.getInput('assetData');

      const project = projectInput?.value;
      const assetData = assetDataInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      if (!assetData) {
        throw new Error('资产数据不能为空');
      }

      // 创建资产对象
      const asset = {
        id: Date.now().toString(),
        ...assetData,
        addedAt: new Date(),
        projectId: project.id
      };

      // 添加到项目
      if (!project.assets) {
        project.assets = [];
      }
      project.assets.push(asset);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const assetOutput = this.getOutput('asset');

      if (completedOutput) completedOutput.value = true;
      if (assetOutput) assetOutput.value = asset;

      return asset;
    } catch (error) {
      console.error('添加资产失败:', error);

      const completedOutput = this.getOutput('completed');
      const assetOutput = this.getOutput('asset');

      if (completedOutput) completedOutput.value = false;
      if (assetOutput) assetOutput.value = null;

      return null;
    }
  }
}

/**
 * 移除资产节点 (260)
 * 从项目移除资产
 */
export class RemoveAssetNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'assetId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '资产ID'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'removed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '移除成功'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const assetIdInput = this.getInput('assetId');

      const project = projectInput?.value;
      const assetId = assetIdInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      if (!assetId) {
        throw new Error('资产ID不能为空');
      }

      // 从项目中移除资产
      if (project.assets) {
        const initialLength = project.assets.length;
        project.assets = project.assets.filter((asset: any) => asset.id !== assetId);
        const removed = project.assets.length < initialLength;

        // 设置输出值
        const completedOutput = this.getOutput('completed');
        const removedOutput = this.getOutput('removed');

        if (completedOutput) completedOutput.value = true;
        if (removedOutput) removedOutput.value = removed;

        return removed;
      }

      return false;
    } catch (error) {
      console.error('移除资产失败:', error);

      const completedOutput = this.getOutput('completed');
      const removedOutput = this.getOutput('removed');

      if (completedOutput) completedOutput.value = false;
      if (removedOutput) removedOutput.value = false;

      return false;
    }
  }
}

/**
 * 获取资产列表节点 (261)
 * 获取项目中的所有资产
 */
export class GetAssetListNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'assetType',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '资产类型过滤'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'assets',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '资产列表'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const assetTypeInput = this.getInput('assetType');

      const project = projectInput?.value;
      const assetType = assetTypeInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      let assets = project.assets || [];

      // 如果指定了资产类型，进行过滤
      if (assetType) {
        assets = assets.filter((asset: any) => asset.type === assetType);
      }

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const assetsOutput = this.getOutput('assets');

      if (completedOutput) completedOutput.value = true;
      if (assetsOutput) assetsOutput.value = assets;

      return assets;
    } catch (error) {
      console.error('获取资产列表失败:', error);

      const completedOutput = this.getOutput('completed');
      const assetsOutput = this.getOutput('assets');

      if (completedOutput) completedOutput.value = false;
      if (assetsOutput) assetsOutput.value = [];

      return [];
    }
  }
}

/**
 * 创建场景节点 (262)
 * 在项目中创建新场景
 */
export class CreateSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'sceneName',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '场景名称',
      defaultValue: '新场景'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '场景对象'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const sceneNameInput = this.getInput('sceneName');

      const project = projectInput?.value;
      const sceneName = sceneNameInput?.value || '新场景';

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 创建场景对象
      const scene = {
        id: Date.now().toString(),
        name: sceneName,
        entities: [],
        cameras: [],
        lights: [],
        createdAt: new Date(),
        projectId: project.id
      };

      // 添加到项目
      if (!project.scenes) {
        project.scenes = [];
      }
      project.scenes.push(scene);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const sceneOutput = this.getOutput('scene');

      if (completedOutput) completedOutput.value = true;
      if (sceneOutput) sceneOutput.value = scene;

      return scene;
    } catch (error) {
      console.error('创建场景失败:', error);

      const completedOutput = this.getOutput('completed');
      const sceneOutput = this.getOutput('scene');

      if (completedOutput) completedOutput.value = false;
      if (sceneOutput) sceneOutput.value = null;

      return null;
    }
  }
}

/**
 * 删除场景节点 (263)
 * 从项目中删除场景
 */
export class DeleteSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'sceneId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '场景ID'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'deleted',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '删除成功'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const sceneIdInput = this.getInput('sceneId');

      const project = projectInput?.value;
      const sceneId = sceneIdInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      if (!sceneId) {
        throw new Error('场景ID不能为空');
      }

      // 从项目中删除场景
      if (project.scenes) {
        const initialLength = project.scenes.length;
        project.scenes = project.scenes.filter((scene: any) => scene.id !== sceneId);
        const deleted = project.scenes.length < initialLength;

        // 设置输出值
        const completedOutput = this.getOutput('completed');
        const deletedOutput = this.getOutput('deleted');

        if (completedOutput) completedOutput.value = true;
        if (deletedOutput) deletedOutput.value = deleted;

        return deleted;
      }

      return false;
    } catch (error) {
      console.error('删除场景失败:', error);

      const completedOutput = this.getOutput('completed');
      const deletedOutput = this.getOutput('deleted');

      if (completedOutput) completedOutput.value = false;
      if (deletedOutput) deletedOutput.value = false;

      return false;
    }
  }
}

/**
 * 获取场景列表节点 (264)
 * 获取项目中的所有场景
 */
export class GetSceneListNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'scenes',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '场景列表'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const project = projectInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      const scenes = project.scenes || [];

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const scenesOutput = this.getOutput('scenes');

      if (completedOutput) completedOutput.value = true;
      if (scenesOutput) scenesOutput.value = scenes;

      return scenes;
    } catch (error) {
      console.error('获取场景列表失败:', error);

      const completedOutput = this.getOutput('completed');
      const scenesOutput = this.getOutput('scenes');

      if (completedOutput) completedOutput.value = false;
      if (scenesOutput) scenesOutput.value = [];

      return [];
    }
  }
}

/**
 * 设置活动场景节点 (265)
 * 设置项目的活动场景
 */
export class SetActiveSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'sceneId',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '场景ID'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'activeScene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '活动场景'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const sceneIdInput = this.getInput('sceneId');

      const project = projectInput?.value;
      const sceneId = sceneIdInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      if (!sceneId) {
        throw new Error('场景ID不能为空');
      }

      // 查找场景
      const scene = project.scenes?.find((s: any) => s.id === sceneId);
      if (!scene) {
        throw new Error('场景不存在');
      }

      // 设置活动场景
      project.activeSceneId = sceneId;
      project.activeScene = scene;

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const activeSceneOutput = this.getOutput('activeScene');

      if (completedOutput) completedOutput.value = true;
      if (activeSceneOutput) activeSceneOutput.value = scene;

      return scene;
    } catch (error) {
      console.error('设置活动场景失败:', error);

      const completedOutput = this.getOutput('completed');
      const activeSceneOutput = this.getOutput('activeScene');

      if (completedOutput) completedOutput.value = false;
      if (activeSceneOutput) activeSceneOutput.value = null;

      return null;
    }
  }
}

/**
 * 获取活动场景节点 (266)
 * 获取项目的活动场景
 */
export class GetActiveSceneNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'activeScene',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '活动场景'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const project = projectInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      const activeScene = project.activeScene || null;

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const activeSceneOutput = this.getOutput('activeScene');

      if (completedOutput) completedOutput.value = true;
      if (activeSceneOutput) activeSceneOutput.value = activeScene;

      return activeScene;
    } catch (error) {
      console.error('获取活动场景失败:', error);

      const completedOutput = this.getOutput('completed');
      const activeSceneOutput = this.getOutput('activeScene');

      if (completedOutput) completedOutput.value = false;
      if (activeSceneOutput) activeSceneOutput.value = null;

      return null;
    }
  }
}

/**
 * 构建项目节点 (267)
 * 构建项目为可部署格式
 */
export class BuildProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'buildOptions',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '构建选项'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'buildResult',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '构建结果'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const buildOptionsInput = this.getInput('buildOptions');

      const project = projectInput?.value;
      const buildOptions = buildOptionsInput?.value || {};

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 模拟构建过程
      const buildResult = {
        success: true,
        buildTime: new Date(),
        outputPath: buildOptions.outputPath || './build',
        platform: buildOptions.platform || 'web',
        optimization: buildOptions.optimization || 'production',
        assets: project.assets?.length || 0,
        scenes: project.scenes?.length || 0
      };

      console.log('项目构建完成:', buildResult);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const buildResultOutput = this.getOutput('buildResult');

      if (completedOutput) completedOutput.value = true;
      if (buildResultOutput) buildResultOutput.value = buildResult;

      return buildResult;
    } catch (error) {
      console.error('构建项目失败:', error);

      const buildResult = {
        success: false,
        error: error.message,
        buildTime: new Date()
      };

      const completedOutput = this.getOutput('completed');
      const buildResultOutput = this.getOutput('buildResult');

      if (completedOutput) completedOutput.value = false;
      if (buildResultOutput) buildResultOutput.value = buildResult;

      return buildResult;
    }
  }
}

/**
 * 发布项目节点 (268)
 * 发布项目到指定平台
 */
export class PublishProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'publishOptions',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '发布选项'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'publishResult',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '发布结果'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const publishOptionsInput = this.getInput('publishOptions');

      const project = projectInput?.value;
      const publishOptions = publishOptionsInput?.value || {};

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 模拟发布过程
      const publishResult = {
        success: true,
        publishTime: new Date(),
        platform: publishOptions.platform || 'web',
        url: publishOptions.url || 'https://example.com/project',
        version: publishOptions.version || '1.0.0',
        projectId: project.id
      };

      console.log('项目发布完成:', publishResult);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const publishResultOutput = this.getOutput('publishResult');

      if (completedOutput) completedOutput.value = true;
      if (publishResultOutput) publishResultOutput.value = publishResult;

      return publishResult;
    } catch (error) {
      console.error('发布项目失败:', error);

      const publishResult = {
        success: false,
        error: error.message,
        publishTime: new Date()
      };

      const completedOutput = this.getOutput('completed');
      const publishResultOutput = this.getOutput('publishResult');

      if (completedOutput) completedOutput.value = false;
      if (publishResultOutput) publishResultOutput.value = publishResult;

      return publishResult;
    }
  }
}

/**
 * 备份项目节点 (269)
 * 创建项目备份
 */
export class BackupProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '项目对象'
    });
    this.addInput({
      name: 'backupPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '备份路径'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'backupInfo',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '备份信息'
    });
  }

  public execute(): any {
    try {
      const projectInput = this.getInput('project');
      const backupPathInput = this.getInput('backupPath');

      const project = projectInput?.value;
      const backupPath = backupPathInput?.value;

      if (!project) {
        throw new Error('项目对象不能为空');
      }

      // 创建备份信息
      const backupInfo = {
        id: Date.now().toString(),
        projectId: project.id,
        projectName: project.name,
        backupTime: new Date(),
        backupPath: backupPath || `./backups/${project.name}_${Date.now()}.backup`,
        size: JSON.stringify(project).length,
        success: true
      };

      console.log('项目备份完成:', backupInfo);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const backupInfoOutput = this.getOutput('backupInfo');

      if (completedOutput) completedOutput.value = true;
      if (backupInfoOutput) backupInfoOutput.value = backupInfo;

      return backupInfo;
    } catch (error) {
      console.error('备份项目失败:', error);

      const backupInfo = {
        success: false,
        error: error.message,
        backupTime: new Date()
      };

      const completedOutput = this.getOutput('completed');
      const backupInfoOutput = this.getOutput('backupInfo');

      if (completedOutput) completedOutput.value = false;
      if (backupInfoOutput) backupInfoOutput.value = backupInfo;

      return backupInfo;
    }
  }
}

/**
 * 恢复项目节点 (270)
 * 从备份恢复项目
 */
export class RestoreProjectNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'backupPath',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '备份路径'
    });
    this.addInput({
      name: 'restoreOptions',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'object',
      description: '恢复选项'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'project',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '恢复的项目'
    });
  }

  public execute(): any {
    try {
      const backupPathInput = this.getInput('backupPath');
      const restoreOptionsInput = this.getInput('restoreOptions');

      const backupPath = backupPathInput?.value;
      const restoreOptions = restoreOptionsInput?.value || {};

      if (!backupPath) {
        throw new Error('备份路径不能为空');
      }

      // 模拟恢复项目
      const restoredProject = {
        id: Date.now().toString(),
        name: restoreOptions.newName || '恢复的项目',
        restoredAt: new Date(),
        originalBackupPath: backupPath,
        scenes: [],
        assets: [],
        settings: {
          version: '1.0.0',
          renderer: 'webgl',
          physics: 'cannon'
        }
      };

      console.log('项目恢复完成:', restoredProject);

      // 设置输出值
      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = true;
      if (projectOutput) projectOutput.value = restoredProject;

      return restoredProject;
    } catch (error) {
      console.error('恢复项目失败:', error);

      const completedOutput = this.getOutput('completed');
      const projectOutput = this.getOutput('project');

      if (completedOutput) completedOutput.value = false;
      if (projectOutput) projectOutput.value = null;

      return null;
    }
  }
}

// ============================================================================
// 导出所有节点类
// ============================================================================

export const EditorProjectNodesBatch9 = {
  // 项目管理节点 (251-258)
  CreateProjectNode,
  OpenProjectNode,
  SaveProjectNode,
  CloseProjectNode,
  CreateProjectTemplateNode,
  ImportProjectNode,
  SetProjectSettingsNode,
  GetProjectInfoNode,

  // 资产管理节点 (259-264)
  AddAssetNode,
  RemoveAssetNode,
  GetAssetListNode,
  CreateSceneNode,
  DeleteSceneNode,
  GetSceneListNode,

  // 场景管理节点 (265-270)
  SetActiveSceneNode,
  GetActiveSceneNode,
  BuildProjectNode,
  PublishProjectNode,
  BackupProjectNode,
  RestoreProjectNode
};
