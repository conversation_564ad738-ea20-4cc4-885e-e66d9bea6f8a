/**
 * UI编辑节点（续）
 * 第10批次：UI编辑和工具辅助节点（节点283-300）
 * 包含UI位置、大小、样式、事件处理、工具辅助等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// UI编辑节点（283-295）
// ============================================================================

/**
 * 设置UI位置节点 (283)
 * 设置UI元素位置
 */
export class SetUIPositionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'x',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: 'X坐标',
      defaultValue: 0
    });
    this.addSocket({
      name: 'y',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: 'Y坐标',
      defaultValue: 0
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const x = inputs.x || 0;
      const y = inputs.y || 0;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置位置
      element.style.left = `${x}px`;
      element.style.top = `${y}px`;

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI位置失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI大小节点 (284)
 * 设置UI元素尺寸
 */
export class SetUISizeNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'width',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 100
    });
    this.addSocket({
      name: 'height',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 100
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const width = inputs.width || 100;
      const height = inputs.height || 100;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置大小
      element.style.width = `${width}px`;
      element.style.height = `${height}px`;

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI大小失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI文本节点 (285)
 * 设置UI元素文本内容
 */
export class SetUITextNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'text',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '文本内容',
      defaultValue: ''
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const text = inputs.text || '';

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 设置文本内容
      if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
        element.value = text;
      } else {
        element.textContent = text;
      }

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI文本失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI颜色节点 (286)
 * 设置UI元素颜色
 */
export class SetUIColorNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'color',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '颜色值',
      defaultValue: '#ffffff'
    });
    this.addSocket({
      name: 'property',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '颜色属性',
      defaultValue: 'color'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const color = inputs.color || '#ffffff';
      const property = inputs.property || 'color';

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置颜色
      element.style[property] = color;

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI颜色失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI字体节点 (287)
 * 设置UI元素字体
 */
export class SetUIFontNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'fontFamily',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '字体族',
      defaultValue: 'Arial'
    });
    this.addSocket({
      name: 'fontSize',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '字体大小',
      defaultValue: 14
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const fontFamily = inputs.fontFamily || 'Arial';
      const fontSize = inputs.fontSize || 14;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      // 设置字体
      element.style.fontFamily = fontFamily;
      element.style.fontSize = `${fontSize}px`;

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI字体失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 设置UI图像节点 (288)
 * 设置UI元素背景图像
 */
export class SetUIImageNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'imageUrl',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '图像URL'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const imageUrl = inputs.imageUrl;

      if (!element || !element.style) {
        throw new Error('UI元素不能为空');
      }

      if (!imageUrl) {
        throw new Error('图像URL不能为空');
      }

      // 设置背景图像
      if (element.tagName === 'IMG') {
        element.src = imageUrl;
      } else {
        element.style.backgroundImage = `url(${imageUrl})`;
        element.style.backgroundSize = 'cover';
        element.style.backgroundPosition = 'center';
      }

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('设置UI图像失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 添加UI事件节点 (289)
 * 为UI元素添加事件
 */
export class AddUIEventNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'eventType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'click'
    });
    this.addSocket({
      name: 'callback',
      type: SocketType.FUNCTION,
      direction: SocketDirection.INPUT,
      description: '回调函数'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const eventType = inputs.eventType || 'click';
      const callback = inputs.callback;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      if (!callback || typeof callback !== 'function') {
        throw new Error('回调函数不能为空');
      }

      // 添加事件监听器
      element.addEventListener(eventType, callback);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('添加UI事件失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}

/**
 * 移除UI事件节点 (290)
 * 移除UI元素事件
 */
export class RemoveUIEventNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });
    this.addSocket({
      name: 'eventType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '事件类型',
      defaultValue: 'click'
    });
    this.addSocket({
      name: 'callback',
      type: SocketType.FUNCTION,
      direction: SocketDirection.INPUT,
      description: '回调函数'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;
      const eventType = inputs.eventType || 'click';
      const callback = inputs.callback;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      if (!callback || typeof callback !== 'function') {
        throw new Error('回调函数不能为空');
      }

      // 移除事件监听器
      element.removeEventListener(eventType, callback);

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('移除UI事件失败:', error);
      return {
        completed: false,
        element: inputs.element
      };
    }
  }
}
