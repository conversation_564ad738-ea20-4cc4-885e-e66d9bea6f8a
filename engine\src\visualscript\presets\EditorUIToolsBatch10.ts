/**
 * 编辑器UI与工具系统节点
 * 第10批次：编辑器UI与工具系统（节点271-300）
 * 包含场景编辑、UI编辑、工具辅助等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 场景编辑节点（271-280）
// ============================================================================

/**
 * 取消组合实体节点 (271)
 * 取消实体组合
 */
export class UngroupEntitiesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'group',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体组'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entities',
      type: SocketType.ARRAY,
      direction: SocketDirection.OUTPUT,
      description: '解组后的实体'
    });
  }

  public execute(inputs: any): any {
    try {
      const group = inputs.group;
      const scene = inputs.scene;

      if (!group || !scene) {
        throw new Error('实体组和场景对象不能为空');
      }

      const entities = [];

      // 将组中的实体移动到场景中
      while (group.children.length > 0) {
        const entity = group.children[0];
        group.remove(entity);
        scene.add(entity);
        entities.push(entity);
      }

      // 移除空的组
      if (group.parent) {
        group.parent.remove(group);
      }

      return {
        completed: true,
        entities: entities
      };
    } catch (error) {
      console.error('取消组合实体失败:', error);
      return {
        completed: false,
        entities: []
      };
    }
  }
}

/**
 * 设置实体父对象节点 (272)
 * 设置实体的父子关系
 */
export class SetEntityParentNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '子实体'
    });
    this.addSocket({
      name: 'parent',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '父实体'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '子实体'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const parent = inputs.parent;

      if (!entity || !parent) {
        throw new Error('实体和父对象不能为空');
      }

      // 从当前父对象中移除
      if (entity.parent) {
        entity.parent.remove(entity);
      }

      // 添加到新的父对象
      parent.add(entity);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('设置实体父对象失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 移动实体节点 (273)
 * 移动实体位置
 */
export class MoveEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addSocket({
      name: 'position',
      type: SocketType.VECTOR3,
      direction: SocketDirection.INPUT,
      description: '新位置',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const position = inputs.position || new THREE.Vector3(0, 0, 0);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体位置
      entity.position.copy(position);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('移动实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 旋转实体节点 (274)
 * 旋转实体角度
 */
export class RotateEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addSocket({
      name: 'rotation',
      type: SocketType.VECTOR3,
      direction: SocketDirection.INPUT,
      description: '旋转角度',
      defaultValue: new THREE.Vector3(0, 0, 0)
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const rotation = inputs.rotation || new THREE.Vector3(0, 0, 0);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体旋转
      entity.rotation.set(rotation.x, rotation.y, rotation.z);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('旋转实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 缩放实体节点 (275)
 * 缩放实体大小
 */
export class ScaleEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addSocket({
      name: 'scale',
      type: SocketType.VECTOR3,
      direction: SocketDirection.INPUT,
      description: '缩放比例',
      defaultValue: new THREE.Vector3(1, 1, 1)
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const scale = inputs.scale || new THREE.Vector3(1, 1, 1);

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 设置实体缩放
      entity.scale.copy(scale);

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('缩放实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 隐藏实体节点 (276)
 * 隐藏场景实体
 */
export class HideEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 隐藏实体
      entity.visible = false;

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('隐藏实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 显示实体节点 (277)
 * 显示场景实体
 */
export class ShowEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 显示实体
      entity.visible = true;

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('显示实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 锁定实体节点 (278)
 * 锁定实体编辑
 */
export class LockEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 锁定实体编辑
      entity.userData = entity.userData || {};
      entity.userData.locked = true;

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('锁定实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 解锁实体节点 (279)
 * 解锁实体编辑
 */
export class UnlockEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '实体对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;

      if (!entity) {
        throw new Error('实体对象不能为空');
      }

      // 解锁实体编辑
      entity.userData = entity.userData || {};
      entity.userData.locked = false;

      return {
        completed: true,
        entity: entity
      };
    } catch (error) {
      console.error('解锁实体失败:', error);
      return {
        completed: false,
        entity: inputs.entity
      };
    }
  }
}

/**
 * 聚焦实体节点 (280)
 * 相机聚焦到实体
 */
export class FocusOnEntityNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'entity',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '实体对象'
    });
    this.addSocket({
      name: 'camera',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '相机对象'
    });
    this.addSocket({
      name: 'distance',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '聚焦距离',
      defaultValue: 10
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'cameraPosition',
      type: SocketType.VECTOR3,
      direction: SocketDirection.OUTPUT,
      description: '相机位置'
    });
  }

  public execute(inputs: any): any {
    try {
      const entity = inputs.entity;
      const camera = inputs.camera;
      const distance = inputs.distance || 10;

      if (!entity || !camera) {
        throw new Error('实体对象和相机对象不能为空');
      }

      // 计算实体的包围盒
      const box = new THREE.Box3().setFromObject(entity);
      const center = box.getCenter(new THREE.Vector3());
      const size = box.getSize(new THREE.Vector3());

      // 计算合适的相机位置
      const maxDim = Math.max(size.x, size.y, size.z);
      const fov = camera.fov * (Math.PI / 180);
      const cameraDistance = Math.abs(maxDim / Math.sin(fov / 2)) + distance;

      // 设置相机位置
      const direction = new THREE.Vector3(1, 1, 1).normalize();
      const cameraPosition = center.clone().add(direction.multiplyScalar(cameraDistance));

      camera.position.copy(cameraPosition);
      camera.lookAt(center);

      return {
        completed: true,
        cameraPosition: cameraPosition
      };
    } catch (error) {
      console.error('聚焦实体失败:', error);
      return {
        completed: false,
        cameraPosition: inputs.camera?.position || new THREE.Vector3()
      };
    }
  }
}

// ============================================================================
// UI编辑节点（281-295）
// ============================================================================

/**
 * 创建UI元素节点 (281)
 * 创建用户界面元素
 */
export class CreateUIElementNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'elementType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: 'UI元素类型',
      defaultValue: 'div'
    });
    this.addSocket({
      name: 'parent',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '父容器'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: 'UI元素'
    });
  }

  public execute(inputs: any): any {
    try {
      const elementType = inputs.elementType || 'div';
      const parent = inputs.parent;

      // 创建UI元素
      const element = document.createElement(elementType);
      element.style.position = 'absolute';
      element.style.left = '0px';
      element.style.top = '0px';
      element.style.width = '100px';
      element.style.height = '100px';

      // 添加到父容器
      if (parent && parent.appendChild) {
        parent.appendChild(element);
      }

      return {
        completed: true,
        element: element
      };
    } catch (error) {
      console.error('创建UI元素失败:', error);
      return {
        completed: false,
        element: null
      };
    }
  }
}

/**
 * 删除UI元素节点 (282)
 * 删除用户界面元素
 */
export class DeleteUIElementNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'element',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: 'UI元素'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'deleted',
      type: SocketType.BOOLEAN,
      direction: SocketDirection.OUTPUT,
      description: '删除成功'
    });
  }

  public execute(inputs: any): any {
    try {
      const element = inputs.element;

      if (!element) {
        throw new Error('UI元素不能为空');
      }

      // 从父容器中移除
      if (element.parentNode) {
        element.parentNode.removeChild(element);
      }

      return {
        completed: true,
        deleted: true
      };
    } catch (error) {
      console.error('删除UI元素失败:', error);
      return {
        completed: false,
        deleted: false
      };
    }
  }
}
