/**
 * 地形与环境系统节点
 * 第9批次：地形与环境系统（节点241-250）
 * 包含水面折射、植被系统、天气系统、环境时间控制等功能
 */

import * as THREE from 'three';
import { Node, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 水面折射节点（241）
// ============================================================================

/**
 * 启用水面折射节点 (241)
 * 启用水面折射效果
 */
export class EnableRefractionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'waterSurface',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });
    this.addInput({
      name: 'refractionStrength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '折射强度',
      defaultValue: 0.5
    });
    this.addInput({
      name: 'refractionIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '折射率',
      defaultValue: 1.33
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'waterSurface',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '水面对象'
    });
  }

  public execute(): any {
    try {
      const waterSurface = this.getInputValue('waterSurface');
      const refractionStrength = this.getInputValue('refractionStrength') || 0.5;
      const refractionIndex = this.getInputValue('refractionIndex') || 1.33;

      if (waterSurface && waterSurface.material) {
        // 启用水面折射效果
        waterSurface.material.transparent = true;
        waterSurface.material.opacity = 0.8;

        // 设置折射参数
        if (waterSurface.material.uniforms) {
          waterSurface.material.uniforms.refractionStrength = { value: refractionStrength };
          waterSurface.material.uniforms.refractionIndex = { value: refractionIndex };
        }

        // 启用折射渲染
        waterSurface.material.needsUpdate = true;
      }

      // 设置输出值
      this.setOutputValue('waterSurface', waterSurface);

      return {
        completed: true,
        waterSurface: waterSurface
      };
    } catch (error) {
      console.error('启用水面折射失败:', error);
      const waterSurface = this.getInputValue('waterSurface');
      this.setOutputValue('waterSurface', waterSurface);
      return {
        completed: false,
        waterSurface: waterSurface
      };
    }
  }
}

// ============================================================================
// 植被系统节点（242-244）
// ============================================================================

/**
 * 创建植被节点 (242)
 * 创建植被系统
 */
export class CreateVegetationNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'terrain',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });
    this.addInput({
      name: 'vegetationType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '植被类型',
      defaultValue: 'mixed'
    });
    this.addInput({
      name: 'density',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '植被密度',
      defaultValue: 0.5
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'vegetationSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '植被系统'
    });
  }

  public execute(): any {
    try {
      const terrain = this.getInputValue('terrain');
      const vegetationType = this.getInputValue('vegetationType') || 'mixed';
      const density = this.getInputValue('density') || 0.5;

      // 创建植被系统
      const vegetationSystem = {
        terrain: terrain,
        type: vegetationType,
        density: density,
        instances: [],
        update: function() {
          // 植被系统更新逻辑
        }
      };

      // 设置输出值
      this.setOutputValue('vegetationSystem', vegetationSystem);

      return {
        completed: true,
        vegetationSystem: vegetationSystem
      };
    } catch (error) {
      console.error('创建植被系统失败:', error);
      this.setOutputValue('vegetationSystem', null);
      return {
        completed: false,
        vegetationSystem: null
      };
    }
  }
}

/**
 * 添加草地节点 (243)
 * 添加草地植被
 */
export class AddGrassNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'vegetationSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '植被系统'
    });
    this.addInput({
      name: 'grassType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '草地类型',
      defaultValue: 'standard'
    });
    this.addInput({
      name: 'coverage',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '覆盖率',
      defaultValue: 0.8
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'grassInstances',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '草地实例'
    });
  }

  public execute(): any {
    try {
      const vegetationSystem = this.getInputValue('vegetationSystem');
      const grassType = this.getInputValue('grassType') || 'standard';
      const coverage = this.getInputValue('coverage') || 0.8;

      const grassInstances = [];

      if (vegetationSystem && vegetationSystem.terrain) {
        // 在地形上生成草地
        const grassCount = Math.floor(1000 * coverage);

        for (let i = 0; i < grassCount; i++) {
          const grassInstance = {
            type: 'grass',
            subType: grassType,
            position: new THREE.Vector3(
              Math.random() * 100 - 50,
              0,
              Math.random() * 100 - 50
            ),
            scale: new THREE.Vector3(
              0.5 + Math.random() * 0.5,
              0.5 + Math.random() * 1.0,
              0.5 + Math.random() * 0.5
            ),
            rotation: Math.random() * Math.PI * 2
          };

          grassInstances.push(grassInstance);
        }

        // 添加到植被系统
        vegetationSystem.instances.push(...grassInstances);
      }

      // 设置输出值
      this.setOutputValue('grassInstances', grassInstances);

      return {
        completed: true,
        grassInstances: grassInstances
      };
    } catch (error) {
      console.error('添加草地失败:', error);
      this.setOutputValue('grassInstances', []);
      return {
        completed: false,
        grassInstances: []
      };
    }
  }
}

/**
 * 添加树木节点 (244)
 * 添加树木植被
 */
export class AddTreesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'vegetationSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '植被系统'
    });
    this.addInput({
      name: 'treeType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '树木类型',
      defaultValue: 'oak'
    });
    this.addInput({
      name: 'density',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '树木密度',
      defaultValue: 0.3
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'treeInstances',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '树木实例'
    });
  }

  public execute(): any {
    try {
      const vegetationSystem = this.getInputValue('vegetationSystem');
      const treeType = this.getInputValue('treeType') || 'oak';
      const density = this.getInputValue('density') || 0.3;

      const treeInstances = [];

      if (vegetationSystem && vegetationSystem.terrain) {
        // 在地形上生成树木
        const treeCount = Math.floor(100 * density);

        for (let i = 0; i < treeCount; i++) {
          const treeInstance = {
            type: 'tree',
            subType: treeType,
            position: new THREE.Vector3(
              Math.random() * 200 - 100,
              0,
              Math.random() * 200 - 100
            ),
            scale: new THREE.Vector3(
              0.8 + Math.random() * 0.4,
              0.8 + Math.random() * 0.6,
              0.8 + Math.random() * 0.4
            ),
            rotation: Math.random() * Math.PI * 2
          };

          treeInstances.push(treeInstance);
        }

        // 添加到植被系统
        vegetationSystem.instances.push(...treeInstances);
      }

      // 设置输出值
      this.setOutputValue('treeInstances', treeInstances);

      return {
        completed: true,
        treeInstances: treeInstances
      };
    } catch (error) {
      console.error('添加树木失败:', error);
      this.setOutputValue('treeInstances', []);
      return {
        completed: false,
        treeInstances: []
      };
    }
  }
}

// ============================================================================
// 天气系统节点（245-249）
// ============================================================================

/**
 * 创建天气系统节点 (245)
 * 创建动态天气系统
 */
export class CreateWeatherSystemNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addInput({
      name: 'weatherType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '天气类型',
      defaultValue: 'clear'
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'weatherSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '天气系统'
    });
  }

  public execute(): any {
    try {
      const scene = this.getInputValue('scene');
      const weatherType = this.getInputValue('weatherType') || 'clear';

      // 创建天气系统
      const weatherSystem = {
        scene: scene,
        currentWeather: weatherType,
        intensity: 0.5,
        windDirection: new THREE.Vector3(1, 0, 0),
        windStrength: 0.3,
        temperature: 20,
        humidity: 0.5,
        particles: [],
        update: function(_deltaTime: number) {
          // 天气系统更新逻辑
        }
      };

      // 设置输出值
      this.setOutputValue('weatherSystem', weatherSystem);

      return {
        completed: true,
        weatherSystem: weatherSystem
      };
    } catch (error) {
      console.error('创建天气系统失败:', error);
      this.setOutputValue('weatherSystem', null);
      return {
        completed: false,
        weatherSystem: null
      };
    }
  }
}

/**
 * 启用雨天节点 (246)
 * 启用雨天效果
 */
export class EnableRainNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'weatherSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '雨量强度',
      defaultValue: 0.5
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'rainEffect',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '雨天效果'
    });
  }

  public execute(): any {
    try {
      const weatherSystem = this.getInputValue('weatherSystem');
      const intensity = this.getInputValue('intensity') || 0.5;

      let rainEffect = null;

      if (weatherSystem) {
        // 创建雨天效果
        rainEffect = {
          type: 'rain',
          intensity: intensity,
          particleCount: Math.floor(1000 * intensity),
          dropSize: 0.1,
          fallSpeed: 10,
          enabled: true
        };

        // 更新天气系统
        weatherSystem.currentWeather = 'rain';
        weatherSystem.intensity = intensity;
        weatherSystem.particles.push(rainEffect);
      }

      // 设置输出值
      this.setOutputValue('rainEffect', rainEffect);

      return {
        completed: true,
        rainEffect: rainEffect
      };
    } catch (error) {
      console.error('启用雨天效果失败:', error);
      this.setOutputValue('rainEffect', null);
      return {
        completed: false,
        rainEffect: null
      };
    }
  }
}

/**
 * 启用雪天节点 (247)
 * 启用雪天效果
 */
export class EnableSnowNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'weatherSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '降雪强度',
      defaultValue: 0.3
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'snowEffect',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '雪天效果'
    });
  }

  public execute(): any {
    try {
      const weatherSystem = this.getInputValue('weatherSystem');
      const intensity = this.getInputValue('intensity') || 0.3;

      let snowEffect = null;

      if (weatherSystem) {
        // 创建雪天效果
        snowEffect = {
          type: 'snow',
          intensity: intensity,
          particleCount: Math.floor(500 * intensity),
          flakeSize: 0.2,
          fallSpeed: 3,
          enabled: true
        };

        // 更新天气系统
        weatherSystem.currentWeather = 'snow';
        weatherSystem.intensity = intensity;
        weatherSystem.temperature = -5;
        weatherSystem.particles.push(snowEffect);
      }

      // 设置输出值
      this.setOutputValue('snowEffect', snowEffect);

      return {
        completed: true,
        snowEffect: snowEffect
      };
    } catch (error) {
      console.error('启用雪天效果失败:', error);
      this.setOutputValue('snowEffect', null);
      return {
        completed: false,
        snowEffect: null
      };
    }
  }
}

/**
 * 设置风向节点 (248)
 * 设置环境风向
 */
export class SetWindDirectionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'weatherSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addInput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.INPUT,
      description: '风向向量',
      defaultValue: new THREE.Vector3(1, 0, 0)
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'windDirection',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '风向向量'
    });
  }

  public execute(): any {
    try {
      const weatherSystem = this.getInputValue('weatherSystem');
      const direction = this.getInputValue('direction') || new THREE.Vector3(1, 0, 0);

      // 标准化风向向量
      const normalizedDirection = direction.clone().normalize();

      if (weatherSystem) {
        // 设置风向
        weatherSystem.windDirection = normalizedDirection;
      }

      // 设置输出值
      this.setOutputValue('windDirection', normalizedDirection);

      return {
        completed: true,
        windDirection: normalizedDirection
      };
    } catch (error) {
      console.error('设置风向失败:', error);
      const direction = this.getInputValue('direction') || new THREE.Vector3(1, 0, 0);
      this.setOutputValue('windDirection', direction);
      return {
        completed: false,
        windDirection: direction
      };
    }
  }
}

/**
 * 设置风力强度节点 (249)
 * 设置环境风力强度
 */
export class SetWindStrengthNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'weatherSystem',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addInput({
      name: 'strength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '风力强度',
      defaultValue: 0.5
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'windStrength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '风力强度'
    });
  }

  public execute(): any {
    try {
      const weatherSystem = this.getInputValue('weatherSystem');
      const strength = Math.max(0, Math.min(1, this.getInputValue('strength') || 0.5));

      if (weatherSystem) {
        // 设置风力强度
        weatherSystem.windStrength = strength;
      }

      // 设置输出值
      this.setOutputValue('windStrength', strength);

      return {
        completed: true,
        windStrength: strength
      };
    } catch (error) {
      console.error('设置风力强度失败:', error);
      const strength = this.getInputValue('strength') || 0.5;
      this.setOutputValue('windStrength', strength);
      return {
        completed: false,
        windStrength: strength
      };
    }
  }
}

// ============================================================================
// 环境时间控制节点（250）
// ============================================================================

/**
 * 设置时间节点 (250)
 * 设置环境时间
 */
export class SetTimeOfDayNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected initializeSockets(): void {
    // 输入
    this.addInput({
      name: 'trigger',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addInput({
      name: 'scene',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addInput({
      name: 'timeOfDay',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间（0-24小时）',
      defaultValue: 12
    });
    this.addInput({
      name: 'sunLight',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '太阳光源',
      optional: true
    });

    // 输出
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addOutput({
      name: 'lightIntensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '光照强度'
    });
    this.addOutput({
      name: 'skyColor',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '天空颜色'
    });
  }

  public execute(): any {
    try {
      const scene = this.getInputValue('scene');
      const timeOfDay = Math.max(0, Math.min(24, this.getInputValue('timeOfDay') || 12));
      const sunLight = this.getInputValue('sunLight');

      // 计算光照强度（基于时间）
      let lightIntensity = 0;
      if (timeOfDay >= 6 && timeOfDay <= 18) {
        // 白天：6点到18点
        const dayProgress = (timeOfDay - 6) / 12;
        lightIntensity = Math.sin(dayProgress * Math.PI) * 0.8 + 0.2;
      } else {
        // 夜晚：18点到6点
        lightIntensity = 0.1;
      }

      // 计算天空颜色
      let skyColor: THREE.Color;
      if (timeOfDay >= 5 && timeOfDay <= 7) {
        // 日出
        skyColor = new THREE.Color(1.0, 0.6, 0.3);
      } else if (timeOfDay >= 17 && timeOfDay <= 19) {
        // 日落
        skyColor = new THREE.Color(1.0, 0.4, 0.2);
      } else if (timeOfDay >= 7 && timeOfDay <= 17) {
        // 白天
        skyColor = new THREE.Color(0.5, 0.7, 1.0);
      } else {
        // 夜晚
        skyColor = new THREE.Color(0.1, 0.1, 0.3);
      }

      // 更新场景光照
      if (sunLight) {
        sunLight.intensity = lightIntensity;

        // 计算太阳位置
        const sunAngle = (timeOfDay / 24) * Math.PI * 2 - Math.PI / 2;
        const sunPosition = new THREE.Vector3(
          Math.cos(sunAngle) * 100,
          Math.sin(sunAngle) * 100,
          0
        );
        sunLight.position.copy(sunPosition);
      }

      // 更新场景背景色
      if (scene && scene.background) {
        scene.background = skyColor;
      }

      // 设置输出值
      this.setOutputValue('lightIntensity', lightIntensity);
      this.setOutputValue('skyColor', skyColor);

      return {
        completed: true,
        lightIntensity: lightIntensity,
        skyColor: skyColor
      };
    } catch (error) {
      console.error('设置环境时间失败:', error);
      this.setOutputValue('lightIntensity', 0.5);
      this.setOutputValue('skyColor', new THREE.Color(0.5, 0.7, 1.0));
      return {
        completed: false,
        lightIntensity: 0.5,
        skyColor: new THREE.Color(0.5, 0.7, 1.0)
      };
    }
  }
}
