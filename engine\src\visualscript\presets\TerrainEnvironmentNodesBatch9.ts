/**
 * 地形与环境系统节点
 * 第9批次：地形与环境系统（节点241-250）
 * 包含水面折射、植被系统、天气系统、环境时间控制等功能
 */

import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { Node } from '../nodes/Node';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';

// ============================================================================
// 水面折射节点（241）
// ============================================================================

/**
 * 启用水面折射节点 (241)
 * 启用水面折射效果
 */
export class EnableRefractionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'waterSurface',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '水面对象'
    });
    this.addSocket({
      name: 'refractionStrength',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '折射强度',
      defaultValue: 0.5
    });
    this.addSocket({
      name: 'refractionIndex',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '折射率',
      defaultValue: 1.33
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'waterSurface',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '水面对象'
    });
  }

  public execute(inputs: any): any {
    try {
      const waterSurface = inputs.waterSurface;
      const refractionStrength = inputs.refractionStrength || 0.5;
      const refractionIndex = inputs.refractionIndex || 1.33;

      if (waterSurface && waterSurface.material) {
        // 启用水面折射效果
        waterSurface.material.transparent = true;
        waterSurface.material.opacity = 0.8;
        
        // 设置折射参数
        if (waterSurface.material.uniforms) {
          waterSurface.material.uniforms.refractionStrength = { value: refractionStrength };
          waterSurface.material.uniforms.refractionIndex = { value: refractionIndex };
        }

        // 启用折射渲染
        waterSurface.material.needsUpdate = true;
      }

      return {
        completed: true,
        waterSurface: waterSurface
      };
    } catch (error) {
      console.error('启用水面折射失败:', error);
      return {
        completed: false,
        waterSurface: inputs.waterSurface
      };
    }
  }
}

// ============================================================================
// 植被系统节点（242-244）
// ============================================================================

/**
 * 创建植被节点 (242)
 * 创建植被系统
 */
export class CreateVegetationNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'terrain',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '地形对象'
    });
    this.addSocket({
      name: 'vegetationType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '植被类型',
      defaultValue: 'mixed'
    });
    this.addSocket({
      name: 'density',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '植被密度',
      defaultValue: 0.5
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'vegetationSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '植被系统'
    });
  }

  public execute(inputs: any): any {
    try {
      const terrain = inputs.terrain;
      const vegetationType = inputs.vegetationType || 'mixed';
      const density = inputs.density || 0.5;

      // 创建植被系统
      const vegetationSystem = {
        terrain: terrain,
        type: vegetationType,
        density: density,
        instances: [],
        update: function() {
          // 植被系统更新逻辑
        }
      };

      return {
        completed: true,
        vegetationSystem: vegetationSystem
      };
    } catch (error) {
      console.error('创建植被系统失败:', error);
      return {
        completed: false,
        vegetationSystem: null
      };
    }
  }
}

/**
 * 添加草地节点 (243)
 * 添加草地植被
 */
export class AddGrassNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'vegetationSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '植被系统'
    });
    this.addSocket({
      name: 'grassType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '草地类型',
      defaultValue: 'standard'
    });
    this.addSocket({
      name: 'coverage',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '覆盖率',
      defaultValue: 0.8
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'grassInstances',
      type: SocketType.ARRAY,
      direction: SocketDirection.OUTPUT,
      description: '草地实例'
    });
  }

  public execute(inputs: any): any {
    try {
      const vegetationSystem = inputs.vegetationSystem;
      const grassType = inputs.grassType || 'standard';
      const coverage = inputs.coverage || 0.8;

      const grassInstances = [];

      if (vegetationSystem && vegetationSystem.terrain) {
        // 在地形上生成草地
        const grassCount = Math.floor(1000 * coverage);
        
        for (let i = 0; i < grassCount; i++) {
          const grassInstance = {
            type: 'grass',
            subType: grassType,
            position: new THREE.Vector3(
              Math.random() * 100 - 50,
              0,
              Math.random() * 100 - 50
            ),
            scale: new THREE.Vector3(
              0.5 + Math.random() * 0.5,
              0.5 + Math.random() * 1.0,
              0.5 + Math.random() * 0.5
            ),
            rotation: Math.random() * Math.PI * 2
          };
          
          grassInstances.push(grassInstance);
        }

        // 添加到植被系统
        vegetationSystem.instances.push(...grassInstances);
      }

      return {
        completed: true,
        grassInstances: grassInstances
      };
    } catch (error) {
      console.error('添加草地失败:', error);
      return {
        completed: false,
        grassInstances: []
      };
    }
  }
}

/**
 * 添加树木节点 (244)
 * 添加树木植被
 */
export class AddTreesNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'vegetationSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '植被系统'
    });
    this.addSocket({
      name: 'treeType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '树木类型',
      defaultValue: 'oak'
    });
    this.addSocket({
      name: 'count',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '树木数量',
      defaultValue: 50
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'treeInstances',
      type: SocketType.ARRAY,
      direction: SocketDirection.OUTPUT,
      description: '树木实例'
    });
  }

  public execute(inputs: any): any {
    try {
      const vegetationSystem = inputs.vegetationSystem;
      const treeType = inputs.treeType || 'oak';
      const count = inputs.count || 50;

      const treeInstances = [];

      if (vegetationSystem && vegetationSystem.terrain) {
        // 在地形上生成树木
        for (let i = 0; i < count; i++) {
          const treeInstance = {
            type: 'tree',
            subType: treeType,
            position: new THREE.Vector3(
              Math.random() * 200 - 100,
              0,
              Math.random() * 200 - 100
            ),
            scale: new THREE.Vector3(
              0.8 + Math.random() * 0.4,
              0.8 + Math.random() * 0.6,
              0.8 + Math.random() * 0.4
            ),
            rotation: Math.random() * Math.PI * 2
          };

          treeInstances.push(treeInstance);
        }

        // 添加到植被系统
        vegetationSystem.instances.push(...treeInstances);
      }

      return {
        completed: true,
        treeInstances: treeInstances
      };
    } catch (error) {
      console.error('添加树木失败:', error);
      return {
        completed: false,
        treeInstances: []
      };
    }
  }
}

// ============================================================================
// 天气系统节点（245-249）
// ============================================================================

/**
 * 创建天气系统节点 (245)
 * 创建动态天气系统
 */
export class CreateWeatherSystemNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'scene',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '场景对象'
    });
    this.addSocket({
      name: 'weatherType',
      type: SocketType.STRING,
      direction: SocketDirection.INPUT,
      description: '天气类型',
      defaultValue: 'clear'
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '天气系统'
    });
  }

  public execute(inputs: any): any {
    try {
      const scene = inputs.scene;
      const weatherType = inputs.weatherType || 'clear';

      // 创建天气系统
      const weatherSystem = {
        scene: scene,
        currentWeather: weatherType,
        rainEnabled: false,
        snowEnabled: false,
        windDirection: new THREE.Vector3(1, 0, 0),
        windStrength: 0.5,
        timeOfDay: 12, // 12点
        update: function(deltaTime: number) {
          // 天气系统更新逻辑
        }
      };

      return {
        completed: true,
        weatherSystem: weatherSystem
      };
    } catch (error) {
      console.error('创建天气系统失败:', error);
      return {
        completed: false,
        weatherSystem: null
      };
    }
  }
}

/**
 * 启用雨效节点 (246)
 * 启用雨天效果
 */
export class EnableRainNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addSocket({
      name: 'intensity',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '雨强度',
      defaultValue: 0.5
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'rainEffect',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '雨效果'
    });
  }

  public execute(inputs: any): any {
    try {
      const weatherSystem = inputs.weatherSystem;
      const intensity = inputs.intensity || 0.5;

      let rainEffect = null;

      if (weatherSystem) {
        // 启用雨效
        weatherSystem.rainEnabled = true;
        weatherSystem.currentWeather = 'rain';

        // 创建雨效果
        rainEffect = {
          intensity: intensity,
          particleCount: Math.floor(1000 * intensity),
          dropSize: 0.1,
          fallSpeed: 10
        };
      }

      return {
        completed: true,
        rainEffect: rainEffect
      };
    } catch (error) {
      console.error('启用雨效失败:', error);
      return {
        completed: false,
        rainEffect: null
      };
    }
  }
}

/**
 * 启用雪效节点 (247)
 * 启用雪天效果
 */
export class EnableSnowNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addSocket({
      name: 'intensity',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '雪强度',
      defaultValue: 0.3
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'snowEffect',
      type: SocketType.OBJECT,
      direction: SocketDirection.OUTPUT,
      description: '雪效果'
    });
  }

  public execute(inputs: any): any {
    try {
      const weatherSystem = inputs.weatherSystem;
      const intensity = inputs.intensity || 0.3;

      let snowEffect = null;

      if (weatherSystem) {
        // 启用雪效
        weatherSystem.snowEnabled = true;
        weatherSystem.currentWeather = 'snow';

        // 创建雪效果
        snowEffect = {
          intensity: intensity,
          particleCount: Math.floor(500 * intensity),
          flakeSize: 0.2,
          fallSpeed: 3
        };
      }

      return {
        completed: true,
        snowEffect: snowEffect
      };
    } catch (error) {
      console.error('启用雪效失败:', error);
      return {
        completed: false,
        snowEffect: null
      };
    }
  }
}

/**
 * 设置风向节点 (248)
 * 设置环境风向
 */
export class SetWindDirectionNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addSocket({
      name: 'direction',
      type: SocketType.VECTOR3,
      direction: SocketDirection.INPUT,
      description: '风向',
      defaultValue: new THREE.Vector3(1, 0, 0)
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'windDirection',
      type: SocketType.VECTOR3,
      direction: SocketDirection.OUTPUT,
      description: '风向'
    });
  }

  public execute(inputs: any): any {
    try {
      const weatherSystem = inputs.weatherSystem;
      const direction = inputs.direction || new THREE.Vector3(1, 0, 0);

      if (weatherSystem) {
        // 设置风向
        weatherSystem.windDirection = direction.clone().normalize();
      }

      return {
        completed: true,
        windDirection: direction
      };
    } catch (error) {
      console.error('设置风向失败:', error);
      return {
        completed: false,
        windDirection: inputs.direction
      };
    }
  }
}

/**
 * 设置风力节点 (249)
 * 设置环境风力强度
 */
export class SetWindStrengthNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addSocket({
      name: 'strength',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '风力强度',
      defaultValue: 0.5
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'windStrength',
      type: SocketType.NUMBER,
      direction: SocketDirection.OUTPUT,
      description: '风力强度'
    });
  }

  public execute(inputs: any): any {
    try {
      const weatherSystem = inputs.weatherSystem;
      const strength = inputs.strength || 0.5;

      if (weatherSystem) {
        // 设置风力强度
        weatherSystem.windStrength = Math.max(0, Math.min(1, strength));
      }

      return {
        completed: true,
        windStrength: strength
      };
    } catch (error) {
      console.error('设置风力失败:', error);
      return {
        completed: false,
        windStrength: inputs.strength
      };
    }
  }
}

// ============================================================================
// 环境时间控制节点（250）
// ============================================================================

/**
 * 设置时间节点 (250)
 * 设置环境时间
 */
export class SetTimeOfDayNode extends Node {
  constructor(options: any) {
    super(options);
  }

  protected setupSockets(): void {
    // 输入
    this.addSocket({
      name: 'trigger',
      type: SocketType.EXEC,
      direction: SocketDirection.INPUT,
      description: '触发执行'
    });
    this.addSocket({
      name: 'weatherSystem',
      type: SocketType.OBJECT,
      direction: SocketDirection.INPUT,
      description: '天气系统'
    });
    this.addSocket({
      name: 'timeOfDay',
      type: SocketType.NUMBER,
      direction: SocketDirection.INPUT,
      description: '时间（小时）',
      defaultValue: 12
    });

    // 输出
    this.addSocket({
      name: 'completed',
      type: SocketType.EXEC,
      direction: SocketDirection.OUTPUT,
      description: '完成执行'
    });
    this.addSocket({
      name: 'timeOfDay',
      type: SocketType.NUMBER,
      direction: SocketDirection.OUTPUT,
      description: '时间（小时）'
    });
  }

  public execute(inputs: any): any {
    try {
      const weatherSystem = inputs.weatherSystem;
      const timeOfDay = inputs.timeOfDay || 12;

      if (weatherSystem) {
        // 设置环境时间
        weatherSystem.timeOfDay = Math.max(0, Math.min(24, timeOfDay));

        // 根据时间调整光照
        if (weatherSystem.scene) {
          const normalizedTime = timeOfDay / 24;
          const lightIntensity = Math.sin(normalizedTime * Math.PI);

          // 更新场景光照
          weatherSystem.scene.traverse((child: any) => {
            if (child.isDirectionalLight) {
              child.intensity = Math.max(0.1, lightIntensity);
            }
          });
        }
      }

      return {
        completed: true,
        timeOfDay: timeOfDay
      };
    } catch (error) {
      console.error('设置环境时间失败:', error);
      return {
        completed: false,
        timeOfDay: inputs.timeOfDay
      };
    }
  }
}
